{"name": "backend", "version": "1.0.0", "description": "", "main": "src/index.ts", "prisma": {"seed": "ts-node prisma/seed.ts"}, "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc -p .", "start": "node dist/index.js", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "db:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "docs:generate": "tsoa spec-and-routes", "prisma:seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.8.2", "@types/express": "^5.0.2", "@types/node": "^22.15.21", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "prisma": "^6.8.2", "swagger-ui-express": "^5.0.1", "tsoa": "^6.6.0", "typescript": "^5.8.3", "zod": "^3.25.28"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/jsonwebtoken": "^9.0.9", "@types/passport": "^1.0.17", "@types/passport-jwt": "^4.0.1", "@types/swagger-ui-express": "^4.1.8", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "tsx": "^4.19.4"}}
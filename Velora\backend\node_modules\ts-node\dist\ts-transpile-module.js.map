{"version": 3, "file": "ts-transpile-module.js", "sourceRoot": "", "sources": ["../src/ts-transpile-module.ts"], "names": [], "mappings": ";;;AAUA,gBAAgB;AAChB,SAAgB,uBAAuB,CACrC,EAAY,EACZ,gBAGC;IAED,MAAM,EACJ,aAAa,EACb,gBAAgB,EAChB,yBAAyB,EACzB,2BAA2B,EAC3B,oBAAoB,EACpB,mCAAmC,EACnC,mBAAmB,EACnB,eAAe,EACf,aAAa,EACb,KAAK,EACL,MAAM,EACN,6BAA6B,EAC7B,UAAU,EACV,QAAQ,EACR,WAAW,EACX,mBAAmB,EACnB,gBAAgB,GACjB,GAAG,EAAS,CAAC;IAEd,MAAM,0BAA0B,GAAiB,EAAE,CAAC;IAEpD,MAAM,OAAO,GAAoB,gBAAgB,CAAC,eAAe;QAC/D,CAAC,CAAC,oBAAoB,CAClB,gBAAgB,CAAC,eAAe,EAChC,0BAA0B,CAC3B;QACH,CAAC,CAAC,EAAE,CAAC;IAEP,yBAAyB;IACzB,MAAM,cAAc,GAAG,yBAAyB,EAAE,CAAC;IACnD,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;QAChC,IAAI,WAAW,CAAC,cAAc,EAAE,GAAG,CAAC,IAAI,OAAO,CAAC,GAAG,CAAC,KAAK,SAAS,EAAE;YAClE,OAAO,CAAC,GAAG,CAAC,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC;SACpC;KACF;IAED,KAAK,MAAM,MAAM,IAAI,mCAAmC,EAAE;QACxD,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,oBAAoB,CAAC;KACpD;IAED,4IAA4I;IAC5I,OAAO,CAAC,uBAAuB,GAAG,IAAI,CAAC;IAEvC,+BAA+B;IAC/B,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC;IAEpC,MAAM,OAAO,GAAG,mBAAmB,CAAC,OAAO,CAAC,CAAC;IAC7C,6EAA6E;IAC7E,MAAM,YAAY,GAAiB;QACjC,aAAa,EAAE,CAAC,QAAQ,EAAE,EAAE,CAC1B,QAAQ,KAAK,aAAa,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,SAAS;QACpE,SAAS,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE;YACxB,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE;gBACjC,KAAK,CAAC,WAAW,CACf,aAAa,EACb,SAAS,EACT,+CAA+C,EAC/C,IAAI,CACL,CAAC;gBACF,aAAa,GAAG,IAAI,CAAC;aACtB;iBAAM;gBACL,KAAK,CAAC,WAAW,CACf,UAAU,EACV,SAAS,EACT,oCAAoC,EACpC,IAAI,CACL,CAAC;gBACF,UAAU,GAAG,IAAI,CAAC;aACnB;QACH,CAAC;QACD,qBAAqB,EAAE,GAAG,EAAE,CAAC,UAAU;QACvC,yBAAyB,EAAE,GAAG,EAAE,CAAC,IAAI;QACrC,oBAAoB,EAAE,CAAC,QAAQ,EAAE,EAAE,CAAC,QAAQ;QAC5C,mBAAmB,EAAE,GAAG,EAAE,CAAC,EAAE;QAC7B,UAAU,EAAE,GAAG,EAAE,CAAC,OAAO;QACzB,UAAU,EAAE,CAAC,QAAQ,EAAW,EAAE,CAChC,QAAQ,KAAK,aAAa,IAAI,QAAQ,KAAK,mBAAmB;QAChE,QAAQ,EAAE,CAAC,QAAQ,EAAE,EAAE,CACrB,QAAQ,KAAK,mBAAmB,CAAC,CAAC,CAAC,aAAa,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAAE;QAC3E,eAAe,EAAE,GAAG,EAAE,CAAC,IAAI;QAC3B,cAAc,EAAE,GAAG,EAAE,CAAC,EAAE;KACzB,CAAC;IAEF,IAAI,aAAqB,CAAC;IAC1B,IAAI,mBAA2B,CAAC;IAChC,IAAI,gBAAuC,CAAC;IAC5C,IAAI,UAAsB,CAAC;IAC3B,IAAI,UAA8B,CAAC;IACnC,IAAI,aAAiC,CAAC;IAEtC,OAAO,eAAe,CAAC;IAEvB,SAAS,eAAe,CACtB,KAAa,EACb,iBAGC,EACD,kBAAyC,UAAU;QAEnD,8CAA8C;QAC9C,aAAa;YACX,iBAAiB,CAAC,QAAQ;gBAC1B,CAAC,gBAAgB,CAAC,eAAe,IAAI,gBAAgB,CAAC,eAAe,CAAC,GAAG;oBACvE,CAAC,CAAC,YAAY;oBACd,CAAC,CAAC,WAAW,CAAC,CAAC;QACnB,mBAAmB,GAAG,gBAAgB,CAAC,aAAa,CAAC,GAAG,eAAe,CAAC;QACxE,gBAAgB,GAAG,eAAe,CAAC;QAEnC,UAAU,GAAG,gBAAgB,CAAC,aAAa,EAAE,KAAK,EAAE;YAClD,eAAe,EAAE,mBAAmB,CAAC,OAAO,CAAC;YAC7C,iBAAiB,EAAE,2BAA2B,CAC5C,MAAM,CAAC,aAAa,EAAE,EAAE,EAAE,YAAY,CAAC,oBAAoB,CAAC;YAC5D,SAAS,CAAC,SAAS,EACnB,YAAY,EACZ,OAAO,CACR;YACD,0BAA0B,EAAE,6BAA6B,CAAC,OAAO,CAAC;SACnE,CAAC,CAAC;QACH,IAAI,iBAAiB,CAAC,UAAU,EAAE;YAChC,UAAU,CAAC,UAAU,GAAG,iBAAiB,CAAC,UAAU,CAAC;SACtD;QAED,IAAI,iBAAiB,CAAC,mBAAmB,EAAE;YACxC,UAAkB,CAAC,mBAAmB,GAAG,IAAI,GAAG,CAC/C,UAAU,CAAC,iBAAiB,CAAC,mBAAmB,CAAC,CAClD,CAAC;SACH;QAED,SAAS;QACT,UAAU,GAAG,SAAS,CAAC;QACvB,aAAa,GAAG,SAAS,CAAC;QAE1B,MAAM,OAAO,GAAG,aAAa,CAAC,CAAC,aAAa,CAAC,EAAE,OAAO,EAAE,YAAY,CAAC,CAAC;QAEtE,MAAM,WAAW,GAAG,0BAA0B,CAAC,KAAK,EAAE,CAAC;QAEvD,IAAI,gBAAgB,CAAC,iBAAiB,EAAE;YACtC,QAAQ;YACN,MAAM,CAAC,WAAW;YAClB,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,UAAU,CAAC,CACrD,CAAC;YACF,QAAQ,CAAC,MAAM,CAAC,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,qBAAqB,EAAE,CAAC,CAAC;SACxE;QACD,OAAO;QACP,OAAO,CAAC,IAAI;QACV,oBAAoB,CAAC,SAAS;QAC9B,aAAa,CAAC,SAAS;QACvB,qBAAqB,CAAC,SAAS;QAC/B,oBAAoB,CAAC,SAAS,EAC9B,gBAAgB,CAAC,YAAY,CAC9B,CAAC;QAEF,IAAI,UAAU,KAAK,SAAS;YAAE,OAAO,KAAK,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;QAE5E,OAAO,EAAE,UAAU,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC;IACpD,CAAC;AACH,CAAC;AArKD,0DAqKC", "sourcesContent": ["import type {\n  CompilerHost,\n  CompilerOptions,\n  Diagnostic,\n  SourceFile,\n  TranspileOptions,\n  TranspileOutput,\n} from 'typescript';\nimport type { TSCommon } from './ts-compiler-types';\n\n/** @internal */\nexport function createTsTranspileModule(\n  ts: TSCommon,\n  transpileOptions: Pick<\n    TranspileOptions,\n    'compilerOptions' | 'reportDiagnostics' | 'transformers'\n  >\n) {\n  const {\n    createProgram,\n    createSourceFile,\n    getDefaultCompilerOptions,\n    getImpliedNodeFormatForFile,\n    fixupCompilerOptions,\n    transpileOptionValueCompilerOptions,\n    getNewLineCharacter,\n    fileExtensionIs,\n    normalizePath,\n    Debug,\n    toPath,\n    getSetExternalModuleIndicator,\n    getEntries,\n    addRange,\n    hasProperty,\n    getEmitScriptTarget,\n    getDirectoryPath,\n  } = ts as any;\n\n  const compilerOptionsDiagnostics: Diagnostic[] = [];\n\n  const options: CompilerOptions = transpileOptions.compilerOptions\n    ? fixupCompilerOptions(\n        transpileOptions.compilerOptions,\n        compilerOptionsDiagnostics\n      )\n    : {};\n\n  // mix in default options\n  const defaultOptions = getDefaultCompilerOptions();\n  for (const key in defaultOptions) {\n    if (hasProperty(defaultOptions, key) && options[key] === undefined) {\n      options[key] = defaultOptions[key];\n    }\n  }\n\n  for (const option of transpileOptionValueCompilerOptions) {\n    options[option.name] = option.transpileOptionValue;\n  }\n\n  // transpileModule does not write anything to disk so there is no need to verify that there are no conflicts between input and output paths.\n  options.suppressOutputPathCheck = true;\n\n  // Filename can be non-ts file.\n  options.allowNonTsExtensions = true;\n\n  const newLine = getNewLineCharacter(options);\n  // Create a compilerHost object to allow the compiler to read and write files\n  const compilerHost: CompilerHost = {\n    getSourceFile: (fileName) =>\n      fileName === normalizePath(inputFileName) ? sourceFile : undefined,\n    writeFile: (name, text) => {\n      if (fileExtensionIs(name, '.map')) {\n        Debug.assertEqual(\n          sourceMapText,\n          undefined,\n          'Unexpected multiple source map outputs, file:',\n          name\n        );\n        sourceMapText = text;\n      } else {\n        Debug.assertEqual(\n          outputText,\n          undefined,\n          'Unexpected multiple outputs, file:',\n          name\n        );\n        outputText = text;\n      }\n    },\n    getDefaultLibFileName: () => 'lib.d.ts',\n    useCaseSensitiveFileNames: () => true,\n    getCanonicalFileName: (fileName) => fileName,\n    getCurrentDirectory: () => '',\n    getNewLine: () => newLine,\n    fileExists: (fileName): boolean =>\n      fileName === inputFileName || fileName === packageJsonFileName,\n    readFile: (fileName) =>\n      fileName === packageJsonFileName ? `{\"type\": \"${_packageJsonType}\"}` : '',\n    directoryExists: () => true,\n    getDirectories: () => [],\n  };\n\n  let inputFileName: string;\n  let packageJsonFileName: string;\n  let _packageJsonType: 'module' | 'commonjs';\n  let sourceFile: SourceFile;\n  let outputText: string | undefined;\n  let sourceMapText: string | undefined;\n\n  return transpileModule;\n\n  function transpileModule(\n    input: string,\n    transpileOptions2: Pick<\n      TranspileOptions,\n      'fileName' | 'moduleName' | 'renamedDependencies'\n    >,\n    packageJsonType: 'module' | 'commonjs' = 'commonjs'\n  ): TranspileOutput {\n    // if jsx is specified then treat file as .tsx\n    inputFileName =\n      transpileOptions2.fileName ||\n      (transpileOptions.compilerOptions && transpileOptions.compilerOptions.jsx\n        ? 'module.tsx'\n        : 'module.ts');\n    packageJsonFileName = getDirectoryPath(inputFileName) + '/package.json';\n    _packageJsonType = packageJsonType;\n\n    sourceFile = createSourceFile(inputFileName, input, {\n      languageVersion: getEmitScriptTarget(options),\n      impliedNodeFormat: getImpliedNodeFormatForFile(\n        toPath(inputFileName, '', compilerHost.getCanonicalFileName),\n        /*cache*/ undefined,\n        compilerHost,\n        options\n      ),\n      setExternalModuleIndicator: getSetExternalModuleIndicator(options),\n    });\n    if (transpileOptions2.moduleName) {\n      sourceFile.moduleName = transpileOptions2.moduleName;\n    }\n\n    if (transpileOptions2.renamedDependencies) {\n      (sourceFile as any).renamedDependencies = new Map(\n        getEntries(transpileOptions2.renamedDependencies)\n      );\n    }\n\n    // Output\n    outputText = undefined;\n    sourceMapText = undefined;\n\n    const program = createProgram([inputFileName], options, compilerHost);\n\n    const diagnostics = compilerOptionsDiagnostics.slice();\n\n    if (transpileOptions.reportDiagnostics) {\n      addRange(\n        /*to*/ diagnostics,\n        /*from*/ program.getSyntacticDiagnostics(sourceFile)\n      );\n      addRange(/*to*/ diagnostics, /*from*/ program.getOptionsDiagnostics());\n    }\n    // Emit\n    program.emit(\n      /*targetSourceFile*/ undefined,\n      /*writeFile*/ undefined,\n      /*cancellationToken*/ undefined,\n      /*emitOnlyDtsFiles*/ undefined,\n      transpileOptions.transformers\n    );\n\n    if (outputText === undefined) return Debug.fail('Output generation failed');\n\n    return { outputText, diagnostics, sourceMapText };\n  }\n}\n"]}
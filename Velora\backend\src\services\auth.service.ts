import bcrypt from 'bcryptjs';
import jwt, { SignOptions } from 'jsonwebtoken';
import { PrismaClient, User } from '@prisma/client';
import { authConfig } from '../config/auth';
import type { StringValue } from 'ms';

const prisma = new PrismaClient();

export class AuthService {
  // Password Hashing
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, authConfig.saltRounds);
  }

  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }

  // JWT Generation
  static generateAccessToken(user: Pick<User, 'id' | 'username' | 'rank'>): string {
    const payload = {
      id: user.id,
      username: user.username,
      rank: user.rank,
    };
    const options: SignOptions = {
      expiresIn: authConfig.jwtExpiration as StringValue,
    };
    return jwt.sign(payload, authConfig.jwtSecret, options);
  }

  static generateRefreshToken(user: Pick<User, 'id'>, jti: string): string {
    const payload = {
      id: user.id,
      jti, // jti (JWT ID) claim for refresh token, can be used for revocation
    };
    const options: SignOptions = {
      expiresIn: authConfig.refreshTokenExpiration as StringValue,
    };
    return jwt.sign(payload, authConfig.jwtSecret, options); // Consider a different secret for refresh tokens
  }

  // JWT Verification
  static verifyToken<T>(token: string): T | null {
    try {
      // When verifying refresh token, might need a different secret if used
      return jwt.verify(token, authConfig.jwtSecret) as T;
    } catch (error) {
      console.error('Token verification failed:', error);
      return null;
    }
  }

  // Store Refresh Token (Example - adjust based on your Session model)
  // The jti (JWT ID) is a good candidate for the token field in the Session model
  // to allow for easier revocation lookups.
  static async storeRefreshToken(userId: string, jwtId: string, expiresAt: Date): Promise<void> {
    await prisma.session.create({
      data: {
        userId: userId,
        token: jwtId, // Store the JWT ID (jti) of the refresh token
        expiresAt: expiresAt,
      },
    });
  }

  // Check if refresh token (by its jti) is valid/not revoked
  static async isRefreshTokenValid(jwtId: string): Promise<boolean> {
    const session = await prisma.session.findUnique({
      where: { token: jwtId },
    });
    return !!session && session.expiresAt > new Date();
  }

  // Invalidate a refresh token (e.g., on logout or password change)
  static async invalidateRefreshToken(jwtId: string): Promise<void> {
    try {
        await prisma.session.delete({
            where: { token: jwtId },
        });
    } catch (error) {
        // Handle cases where the token might not exist (e.g., already deleted)
        console.warn(`Attempted to delete session for jti ${jwtId}, but it was not found.`);
    }
  }

  // Invalidate all refresh tokens for a user (e.g., on password change)
  static async invalidateAllRefreshTokensForUser(userId: string): Promise<void> {
    await prisma.session.deleteMany({
      where: { userId: userId },
    });
  }
}

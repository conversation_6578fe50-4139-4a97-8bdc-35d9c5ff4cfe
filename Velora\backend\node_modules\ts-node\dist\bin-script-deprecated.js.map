{"version": 3, "file": "bin-script-deprecated.js", "sourceRoot": "", "sources": ["../src/bin-script-deprecated.ts"], "names": [], "mappings": ";;;AAEA,+BAA6B;AAE7B,OAAO,CAAC,IAAI,CACV,8EAA8E,EAC9E,mCAAmC,CACpC,CAAC;AAEF,IAAA,UAAI,EAAC,SAAS,EAAE,EAAE,cAAc,EAAE,IAAI,EAAE,CAAC,CAAC", "sourcesContent": ["#!/usr/bin/env node\n\nimport { main } from './bin';\n\nconsole.warn(\n  'ts-script has been deprecated and will be removed in the next major release.',\n  'Please use ts-node-script instead'\n);\n\nmain(undefined, { '--scriptMode': true });\n"]}
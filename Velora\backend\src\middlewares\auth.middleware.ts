import passport from 'passport';
import { Strategy as JwtStrategy, ExtractJwt, StrategyOptions } from 'passport-jwt';
import { PrismaClient, User as PrismaUser, UserRank } from '@prisma/client';
import { Request, Response, NextFunction } from 'express';
import { authConfig } from '../config/auth';

const prisma = new PrismaClient();

// Define our custom user type
export type AuthenticatedUser = Pick<PrismaUser, 'id' | 'username' | 'email' | 'rank'>;

// Augment Express Request type to include 'user'
declare global {
  namespace Express {
    interface Request {
      user?: AuthenticatedUser;
    }
    interface User extends AuthenticatedUser {}
  }
}

const jwtOptions: StrategyOptions = {
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
  secretOrKey: authConfig.jwtSecret,
};

passport.use(
  new JwtStrategy(jwtOptions, async (payload, done) => {
    try {
      const user = await prisma.user.findUnique({
        where: { id: payload.id },
        select: { id: true, username: true, email: true, rank: true }, // Select only necessary fields
      });

      if (user) {
        return done(null, user);
      } else {
        return done(null, false);
        // Or you could create a new account, depending on your strategy
      }
    } catch (error) {
      return done(error, false);
    }
  })
);

export const initializePassport = () => {
  return passport.initialize();
};

export const authenticateJwt = (req: Request, res: Response, next: NextFunction) => {
  passport.authenticate('jwt', { session: false }, (err: any, user: Express.User | false, info: any) => {
    if (err) {
      return next(err);
    }
    if (!user) {
      // info might contain details like 'No auth token' or 'jwt expired'
      const message = info?.message || 'Unauthorized';
      if (message === 'jwt expired') {
        return res.status(401).json({ message: 'Access token expired' });
      }
      return res.status(401).json({ message });
    }
    req.user = user; // Attach user to request object
    next();
  })(req, res, next);
};

// Middleware to check for specific roles
export const authorizeRank = (requiredRanks: UserRank[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user || !req.user.rank) {
      res.status(403).json({ message: 'Forbidden: User rank not available' });
      return;
    }
    if (!requiredRanks.includes(req.user.rank)) {
      res.status(403).json({ message: 'Forbidden: Insufficient rank' });
      return;
    }
    next();
  };
};

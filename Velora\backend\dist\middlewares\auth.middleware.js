"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizeRank = exports.authenticateJwt = exports.initializePassport = void 0;
const passport_1 = __importDefault(require("passport"));
const passport_jwt_1 = require("passport-jwt");
const client_1 = require("@prisma/client");
const auth_1 = require("../config/auth");
const prisma = new client_1.PrismaClient();
const jwtOptions = {
    jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
    secretOrKey: auth_1.authConfig.jwtSecret,
};
passport_1.default.use(new passport_jwt_1.Strategy(jwtOptions, async (payload, done) => {
    try {
        const user = await prisma.user.findUnique({
            where: { id: payload.id },
            select: { id: true, username: true, email: true, rank: true }, // Select only necessary fields
        });
        if (user) {
            return done(null, user);
        }
        else {
            return done(null, false);
            // Or you could create a new account, depending on your strategy
        }
    }
    catch (error) {
        return done(error, false);
    }
}));
const initializePassport = () => {
    return passport_1.default.initialize();
};
exports.initializePassport = initializePassport;
const authenticateJwt = (req, res, next) => {
    passport_1.default.authenticate('jwt', { session: false }, (err, user, info) => {
        if (err) {
            return next(err);
        }
        if (!user) {
            // info might contain details like 'No auth token' or 'jwt expired'
            const message = info?.message || 'Unauthorized';
            if (message === 'jwt expired') {
                return res.status(401).json({ message: 'Access token expired' });
            }
            return res.status(401).json({ message });
        }
        req.user = user; // Attach user to request object
        next();
    })(req, res, next);
};
exports.authenticateJwt = authenticateJwt;
// Middleware to check for specific roles
const authorizeRank = (requiredRanks) => {
    return (req, res, next) => {
        if (!req.user || !req.user.rank) {
            res.status(403).json({ message: 'Forbidden: User rank not available' });
            return;
        }
        if (!requiredRanks.includes(req.user.rank)) {
            res.status(403).json({ message: 'Forbidden: Insufficient rank' });
            return;
        }
        next();
    };
};
exports.authorizeRank = authorizeRank;

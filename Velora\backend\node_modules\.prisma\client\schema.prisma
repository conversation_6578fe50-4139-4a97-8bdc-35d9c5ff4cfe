// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  username     String   @unique
  email        String   @unique
  passwordHash String
  rank         UserRank @default(USER)

  twoFactorEnabled Boolean  @default(false)
  twoFactorSecret  String?
  recoveryCodes    String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sessions    Session[]
  teams       TeamMember[]
  ownedTeams  Team[]       @relation("TeamOwner")
  deployments Deployment[] // User who initiated deployment
}

model Session {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  token     String   @unique // Refresh token
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Team {
  id          String       @id @default(cuid())
  name        String       @unique
  ownerId     String
  owner       User         @relation("TeamOwner", fields: [ownerId], references: [id])
  members     TeamMember[]
  servers     Server[]
  deployments Deployment[] // Deployments associated with this team

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model TeamMember {
  id     String   @id @default(cuid())
  teamId String
  team   Team     @relation(fields: [teamId], references: [id], onDelete: Cascade)
  userId String
  user   User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  role   TeamRole @default(MEMBER)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([teamId, userId])
}

model Server {
  id        String  @id @default(cuid())
  name      String
  ipAddress String?
  hostname  String?
  status    String  @default("UNKNOWN") // e.g., ONLINE, OFFLINE, PROVISIONING
  teamId    String
  team      Team    @relation(fields: [teamId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Deployment {
  id         String           @id @default(cuid())
  name       String // e.g., "Frontend Update v1.2"
  status     DeploymentStatus @default(PENDING)
  commitHash String?
  branch     String?
  logs       String?          @db.Text

  userId String // User who initiated
  user   User   @relation(fields: [userId], references: [id])
  teamId String
  team   Team   @relation(fields: [teamId], references: [id])

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model RegistrationCode {
  id        String   @id @default(cuid())
  code      String   @unique
  expiresAt DateTime
  used      Boolean  @default(false)
  usedBy    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum UserRank {
  USER
  ADMIN
  SUPERADMIN
}

enum TeamRole {
  MEMBER
  MODERATOR
  ADMIN
}

enum DeploymentStatus {
  PENDING
  IN_PROGRESS
  SUCCESSFUL
  FAILED
  CANCELLED
}

"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const client_1 = require("@prisma/client");
const auth_1 = require("../config/auth");
const prisma = new client_1.PrismaClient();
class AuthService {
    // Password Hashing
    static async hashPassword(password) {
        return bcryptjs_1.default.hash(password, auth_1.authConfig.saltRounds);
    }
    static async comparePassword(password, hash) {
        return bcryptjs_1.default.compare(password, hash);
    }
    // JWT Generation
    static generateAccessToken(user) {
        const payload = {
            id: user.id,
            username: user.username,
            rank: user.rank,
        };
        const options = {
            expiresIn: auth_1.authConfig.jwtExpiration,
        };
        return jsonwebtoken_1.default.sign(payload, auth_1.authConfig.jwtSecret, options);
    }
    static generateRefreshToken(user, jti) {
        const payload = {
            id: user.id,
            jti, // jti (JWT ID) claim for refresh token, can be used for revocation
        };
        const options = {
            expiresIn: auth_1.authConfig.refreshTokenExpiration,
        };
        return jsonwebtoken_1.default.sign(payload, auth_1.authConfig.jwtSecret, options); // Consider a different secret for refresh tokens
    }
    // JWT Verification
    static verifyToken(token) {
        try {
            // When verifying refresh token, might need a different secret if used
            return jsonwebtoken_1.default.verify(token, auth_1.authConfig.jwtSecret);
        }
        catch (error) {
            console.error('Token verification failed:', error);
            return null;
        }
    }
    // Store Refresh Token (Example - adjust based on your Session model)
    // The jti (JWT ID) is a good candidate for the token field in the Session model
    // to allow for easier revocation lookups.
    static async storeRefreshToken(userId, jwtId, expiresAt) {
        await prisma.session.create({
            data: {
                userId: userId,
                token: jwtId, // Store the JWT ID (jti) of the refresh token
                expiresAt: expiresAt,
            },
        });
    }
    // Check if refresh token (by its jti) is valid/not revoked
    static async isRefreshTokenValid(jwtId) {
        const session = await prisma.session.findUnique({
            where: { token: jwtId },
        });
        return !!session && session.expiresAt > new Date();
    }
    // Invalidate a refresh token (e.g., on logout or password change)
    static async invalidateRefreshToken(jwtId) {
        try {
            await prisma.session.delete({
                where: { token: jwtId },
            });
        }
        catch (error) {
            // Handle cases where the token might not exist (e.g., already deleted)
            console.warn(`Attempted to delete session for jti ${jwtId}, but it was not found.`);
        }
    }
    // Invalidate all refresh tokens for a user (e.g., on password change)
    static async invalidateAllRefreshTokensForUser(userId) {
        await prisma.session.deleteMany({
            where: { userId: userId },
        });
    }
}
exports.AuthService = AuthService;
